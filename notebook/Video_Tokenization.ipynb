# !git clone https://github.com/NVIDIA/Cosmos-Tokenizer.git

# # Step 2: # Install Cosmos-Tokenizer and its Python dependencies.
# import os
# if os.path.exists("Cosmos-Tokenizer"):
#     os.chdir("Cosmos-Tokenizer")
#     !apt-get update
#     !apt-get install -y git-lfs
#     !git lfs pull
#     %pip install -e .
# else:
#     print('Cosmos-Tokenizer is already installed.')

import os

# Check if the token is already set
if "HUGGINGFACE_TOKEN" not in os.environ:
    os.environ["HUGGINGFACE_TOKEN"] = "*************************************"
!git config --global credential.helper store

from huggingface_hub import login, snapshot_download
import os
HUGGINGFACE_TOKEN = os.environ.get("HUGGINGFACE_TOKEN")
login(token=HUGGINGFACE_TOKEN, add_to_git_credential=True)
model_names = [
        "Cosmos-0.1-Tokenizer-CV4x8x8",
        "Cosmos-0.1-Tokenizer-CV8x8x8",
        "Cosmos-0.1-Tokenizer-CV8x16x16",
        "Cosmos-0.1-Tokenizer-DV4x8x8",
        "Cosmos-0.1-Tokenizer-DV8x8x8",
        "Cosmos-0.1-Tokenizer-DV8x16x16",
        "Cosmos-1.0-Tokenizer-CV8x8x8",
        "Cosmos-1.0-Tokenizer-DV8x16x16",
]
for model_name in model_names:
    hf_repo = "nvidia/" + model_name
    local_dir = "pretrained_ckpts/" + model_name
    os.makedirs(local_dir, exist_ok=True)
    print(f"downloading {model_name}...")
    snapshot_download(repo_id=hf_repo, local_dir=local_dir)

# @title In this step, load the required checkpoints, and perform video reconstruction. {"run":"auto"}
import cv2
import numpy as np
import torch

import importlib
import cosmos_tokenizer.video_lib
import mediapy as media

importlib.reload(cosmos_tokenizer.video_lib)
from cosmos_tokenizer.video_lib import CausalVideoTokenizer

# 1) Specify the model name, and the paths to the encoder/decoder checkpoints.
model_name = 'Cosmos-1.0-Tokenizer-CV8x8x8' # @param ["Cosmos-0.1-Tokenizer-CV4x8x8", "Cosmos-0.1-Tokenizer-CV8x8x8", "Cosmos-0.1-Tokenizer-CV8x16x16", "Cosmos-0.1-Tokenizer-DV4x8x8", "Cosmos-0.1-Tokenizer-DV8x8x8", "Cosmos-0.1-Tokenizer-DV8x16x16", "Cosmos-1.0-Tokenizer-CV8x8x8", "Cosmos-1.0-Tokenizer-DV8x16x16"]
temporal_window = 49 # @param {type:"slider", min:1, max:121, step:8}

encoder_ckpt = f"pretrained_ckpts/{model_name}/encoder.jit"
decoder_ckpt = f"pretrained_ckpts/{model_name}/decoder.jit"

# 2) Load or provide the video filename you want to tokenize & reconstruct.
input_filepath = "test_data/gait_video.avi"
# input_filepath = "test_data/video.mp4"

# 3) Read the video from disk (shape = T x H x W x 3 in BGR).
input_video = media.read_video(input_filepath)[..., :3]
assert input_video.ndim == 4 and input_video.shape[-1] == 3, "Frames must have shape T x H x W x 3"

# 4) Expand dimensions to B x Tx H x W x C, since the CausalVideoTokenizer expects a batch dimension
#    in the input. (Batch size = 1 in this example.)
batched_input_video = np.expand_dims(input_video, axis=0)

# 5) Create the CausalVideoTokenizer instance with the encoder & decoder.
#    - device="cuda" uses the GPU
#    - dtype="bfloat16" expects Ampere or newer GPU (A100, RTX 30xx, etc.)
tokenizer = CausalVideoTokenizer(
    checkpoint_enc=encoder_ckpt,
    checkpoint_dec=decoder_ckpt,
    device="cuda",
    dtype="bfloat16",
)

# 6) Use the tokenizer to autoencode (encode & decode) the video.
#    The output is a NumPy array with shape = B x T x H x W x C, range [0..255].
batched_output_video = tokenizer(batched_input_video,
                                 temporal_window=temporal_window)

# 7) Extract the single video from the batch (index 0).
output_video = batched_output_video[0]

# 9) Save the reconstructed video to disk.
input_dir, input_filename = os.path.split(input_filepath)
filename, ext = os.path.splitext(input_filename)
output_filepath = f"{input_dir}/{filename}_{model_name.split('-')[-1]}{ext}"
media.write_video(output_filepath, output_video)
print("Input video read from:\t", f"{os.getcwd()}/{input_filepath}")
print("Reconstruction saved:\t", f"{os.getcwd()}/{output_filepath}")

# 10) Visualization of the input video (left) and the reconstruction (right).
media.show_videos([input_video, output_video], ["Input Video", "Reconstructed Video"], height=720, fps=30)

from cosmos_tokenizer.utils import (
    numpy2tensor,
    pad_video_batch,
    tensor2numpy,
)
from tqdm import tqdm


video = batched_input_video
num_frames = video.shape[1]  # can be of any length.

for idx in tqdm(range(0, (num_frames - 1) // temporal_window + 1)):
    # Input video for the current window.
    start, end = idx * temporal_window, (idx + 1) * temporal_window
    input_video = video[:, start:end, ...]

    # Spatio-temporally pad input_video so it's evenly divisible.
    padded_input_video, crop_region = pad_video_batch(input_video)
    input_tensor = numpy2tensor(
        padded_input_video, dtype=tokenizer._dtype, device=tokenizer._device
    )
    encoding = tokenizer.encode(input_tensor)

    print('encoding: ', encoding[0].shape)
    #padded_output_video = tensor2numpy(encoding)

video.shape

tokenizer._enc_model??

